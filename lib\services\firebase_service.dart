import 'dart:async';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:firebase_core/firebase_core.dart'; // <-- الاستيراد المضاف
import 'package:school_management_system/models/assignment_model.dart';
import 'package:school_management_system/models/class_model.dart';
import 'package:school_management_system/models/fee_type_model.dart';
import 'package:school_management_system/models/guardian_model.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/models/subject_model.dart';
import 'package:school_management_system/models/user_model.dart';
import 'package:school_management_system/models/note_model.dart';
import 'package:school_management_system/models/communication_model.dart';
import 'package:school_management_system/models/exam_model.dart';
import 'package:school_management_system/models/exam_schedule_model.dart';
import 'package:school_management_system/models/exam_syllabus_model.dart';
import 'package:school_management_system/models/grade_entry_model.dart';
import 'package:school_management_system/models/exam_analytics_model.dart';

/// كلاس مركزي لإدارة جميع عمليات Firebase (Firestore و Authentication و Storage)
class FirebaseService {
  // إنشاء نسخة وحيدة من الخدمات لضمان عدم تكرارها في التطبيق
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final FirebaseMessaging _fcm = FirebaseMessaging.instance;
  final FirebaseFunctions _functions = FirebaseFunctions.instanceFor(
    region: 'us-central1',
  ); // تحديد المنطقة مهم
  final ImagePicker _picker = ImagePicker();

  // --- الدوال الأساسية لإدارة المستخدمين (عبر الدوال السحابية) ---

  // --- دوال الإشعارات (FCM) ---

  Future<void> initNotifications() async {
    try {
      // طلب إذن الإشعارات من المستخدم
      await _fcm.requestPermission();
    } catch (e) {
      // تجاهل الخطأ إذا قام المستخدم بحظر الأذونات في المتصفح
      if (kDebugMode) {
        print('Failed to get notification permission: $e');
      }
    }

    // الحصول على توكن FCM
    final fcmToken = await getDeviceToken();
    if (kDebugMode) {
      print('FCM Token: $fcmToken');
    }

    // TODO: حفظ التوكن في Firestore لربطه بالمستخدم الحالي
    //  if (fcmToken != null) {
    //    final user = _auth.currentUser;
    //    if (user != null) {
    //      await _firestore.collection('users').doc(user.uid).update({
    //        'fcmToken': fcmToken,
    //      });
    //    }
    //  }

    // التعامل مع الإشعارات عند فتح التطبيق من خلالها
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      if (kDebugMode) {
        print('A new onMessageOpenedApp event was published!');
        print('Message data: ${message.data}');
      }
      // يمكنك هنا توجيه المستخدم إلى شاشة معينة بناءً على بيانات الإشعار
    });

    // التعامل مع الإشعارات والتطبيق في المقدمة
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      if (kDebugMode) {
        print('Got a message whilst in the foreground!');
        print('Message data: ${message.data}');
      }

      if (message.notification != null) {
        if (kDebugMode) {
          print(
            'Message also contained a notification: ${message.notification}',
          );
        }
        // يمكنك هنا عرض إشعار محلي باستخدام flutter_local_notifications
      }
    });
  }

  Future<String?> getDeviceToken() async {
    return await _fcm.getToken();
  }

  // --- دوال المصادقة (Authentication) ---

  Future<User?> signInWithEmailAndPassword(
    String email,
    String password,
  ) async {
    try {
      UserCredential result = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      return result.user;
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في تسجيل الدخول: $e');
      }
      return null;
    }
  }

  Future<void> signOut() async {
    await _auth.signOut();
  }

  /// دالة لإرسال بريد إلكتروني لإعادة تعيين كلمة المرور.
  Future<void> sendPasswordResetEmail(String email) async {
    await _auth.sendPasswordResetEmail(email: email);
  }

  User? getCurrentUser() {
    return _auth.currentUser;
  }

  Stream<User?> get authStateChanges => _auth.authStateChanges();

  Future<String?> getUserRole(String uid) async {
    try {
      final doc = await _firestore.collection('users').doc(uid).get();
      if (doc.exists) {
        return doc.data()?['role'];
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting user role: $e');
      }
      return null;
    }
  }

  // --- دوال خاصة بالطلاب ---

  Future<String?> _uploadStudentImage(
    Uint8List imageBytes,
    String studentId,
  ) async {
    // تم تبسيط الدالة للسماح بظهور الأخطاء بشكل مباشر في الدالة التي تستدعيها
    final ref = _storage.ref().child('student_images').child('$studentId.jpg');
    final metadata = SettableMetadata(contentType: 'image/jpeg');
    await ref.putData(imageBytes, metadata);
    return await ref.getDownloadURL();
  }

  /// دالة محدثة لإضافة طالب مع جميع الحقول الجديدة المطلوبة للمدارس اليمنية
  Future<void> addStudent(
    String name,
    String number,
    String? classId,
    String? className,
    String email,
    String password,
    String? gender,
    Uint8List? imageBytes, {
    // ===== الحقول الشخصية الجديدة =====
    DateTime? dateOfBirth,
    String? phoneNumber,
    String? address,
    // ===== الحقول المطلوبة للمدارس اليمنية =====
    String? nationalId,
    required String guardianName,
    String? guardianPhone,
    required String governorate,
    String nationality = 'يمني',
    String? bloodType,
    String? healthCondition,
    String? notes,
  }) async {
    FirebaseApp? tempApp;
    try {
      tempApp = await Firebase.initializeApp(
        name: 'tempStudentCreation',
        options: Firebase.app().options,
      );
      final tempAuth = FirebaseAuth.instanceFor(app: tempApp);

      final userCredential = await tempAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      final user = userCredential.user;
      if (user == null) {
        throw Exception('فشل إنشاء حساب الطالب في نظام المصادقة.');
      }
      await user.updateDisplayName(name);
      final studentId = user.uid;

      // رفع الصورة أولاً للحصول على الرابط
      String? imageUrl;
      if (imageBytes != null) {
        imageUrl = await _uploadStudentImage(imageBytes, studentId);
      }

      // إنشاء مستند الطالب في Firestore مع جميع الحقول الجديدة
      await _firestore.collection('students').doc(studentId).set({
        // ===== الحقول الأساسية =====
        'name': name,
        'student_number': number,
        'class': className ?? '',
        'classId': classId,
        'gender': gender,
        'imageUrl': imageUrl,
        'role': 'student',
        'email': email,
        'createdAt': FieldValue.serverTimestamp(),
        'isActive': true,

        // ===== الحقول الشخصية =====
        'dateOfBirth':
            dateOfBirth != null ? Timestamp.fromDate(dateOfBirth) : null,
        'phoneNumber': phoneNumber,
        'address': address,

        // ===== الحقول الجديدة للمدارس اليمنية =====
        'nationalId': nationalId,
        'guardianName': guardianName,
        'guardianPhone': guardianPhone,
        'governorate': governorate,
        'nationality': nationality,
        'bloodType': bloodType,
        'healthCondition': healthCondition,
        'notes': notes,
      });

      // إنشاء مستند مطابق في 'users' لتسهيل الاستعلامات العامة
      await _firestore.collection('users').doc(studentId).set({
        'displayName': name,
        'email': email,
        'role': 'student',
      });

      // تحديث قائمة الطلاب في الفصل
      if (classId != null) {
        await _firestore.collection('classes').doc(classId).update({
          'students': FieldValue.arrayUnion([studentId]),
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error adding student client-side: $e');
      }
      rethrow;
    } finally {
      if (tempApp != null) {
        await tempApp.delete();
      }
    }
  }

  /// دالة محدثة لتحديث بيانات الطالب مع جميع الحقول الجديدة
  Future<void> updateStudent(
    String id,
    String name,
    String number,
    String? newClassId,
    String? newClassName,
    String? oldClassId,
    String? gender,
    Uint8List? imageBytes, {
    // ===== الحقول الشخصية الجديدة =====
    DateTime? dateOfBirth,
    String? phoneNumber,
    String? address,
    // ===== الحقول المطلوبة للمدارس اليمنية =====
    String? nationalId,
    String? guardianName,
    String? guardianPhone,
    String? governorate,
    String? nationality,
    String? bloodType,
    String? healthCondition,
    String? notes,
  }) async {
    String? imageUrl;
    if (imageBytes != null) {
      imageUrl = await _uploadStudentImage(imageBytes, id);
    }

    // إعداد البيانات المحدثة مع جميع الحقول الجديدة
    final Map<String, dynamic> updatedData = {
      // ===== الحقول الأساسية =====
      'name': name,
      'student_number': number,
      'class': newClassName ?? '',
      'classId': newClassId,
      'gender': gender,
    };

    // ===== إضافة الحقول الشخصية إذا تم تمريرها =====
    if (dateOfBirth != null) {
      updatedData['dateOfBirth'] = Timestamp.fromDate(dateOfBirth);
    }
    if (phoneNumber != null) {
      updatedData['phoneNumber'] = phoneNumber;
    }
    if (address != null) {
      updatedData['address'] = address;
    }

    // ===== إضافة الحقول الجديدة للمدارس اليمنية إذا تم تمريرها =====
    if (nationalId != null) {
      updatedData['nationalId'] = nationalId;
    }
    if (guardianName != null) {
      updatedData['guardianName'] = guardianName;
    }
    if (guardianPhone != null) {
      updatedData['guardianPhone'] = guardianPhone;
    }
    if (governorate != null) {
      updatedData['governorate'] = governorate;
    }
    if (nationality != null) {
      updatedData['nationality'] = nationality;
    }
    if (bloodType != null) {
      updatedData['bloodType'] = bloodType;
    }
    if (healthCondition != null) {
      updatedData['healthCondition'] = healthCondition;
    }
    if (notes != null) {
      updatedData['notes'] = notes;
    }

    // ===== إضافة رابط الصورة إذا تم رفعها =====
    if (imageUrl != null) {
      updatedData['imageUrl'] = imageUrl;
    }

    await _firestore.collection('students').doc(id).update(updatedData);

    if (newClassId != oldClassId) {
      if (oldClassId != null) {
        await _firestore.collection('classes').doc(oldClassId).update({
          'students': FieldValue.arrayRemove([id]),
        });
      }
      if (newClassId != null) {
        await _firestore.collection('classes').doc(newClassId).update({
          'students': FieldValue.arrayUnion([id]),
        });
      }
    }
  }

  /// (تم التعديل) دالة حذف طالب من جانب العميل
  Future<void> deleteStudent(StudentModel student) async {
    // ملاحظة: هذا سيحذف البيانات من Firestore فقط.
    // سيبقى الحساب في Firebase Authentication كحساب يتيم.
    // هذا حل بديل لتجنب الحاجة لخطة Blaze.
    try {
      // حذف مستند الطالب من مجموعة 'students'
      await _firestore.collection('students').doc(student.id).delete();
      // حذف مستند المستخدم من مجموعة 'users'
      await _firestore.collection('users').doc(student.id).delete();

      // تحديث قائمة الطلاب في الفصل بعد حذف الطالب
      if (student.classId != null && student.classId!.isNotEmpty) {
        await _firestore.collection('classes').doc(student.classId).update({
          'students': FieldValue.arrayRemove([student.id]),
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting student client-side: $e');
      }
      rethrow;
    }
  }

  // --- دوال قاعدة البيانات (Firestore) ---

  Stream<List<T>> getCollection<T>({
    required String path,
    required T Function(Map<String, dynamic> data, String documentId) builder,
  }) {
    final collection = _firestore.collection(path);
    final snapshots = collection.snapshots();
    return snapshots.map(
      (snapshot) =>
          snapshot.docs.map((doc) => builder(doc.data(), doc.id)).toList(),
    );
  }

  Future<DocumentReference> addDocument(
    String collectionPath,
    Map<String, dynamic> data,
  ) {
    return _firestore.collection(collectionPath).add(data);
  }

  Future<void> updateDocument(
    String collectionPath,
    String docId,
    Map<String, dynamic> data,
  ) {
    return _firestore.collection(collectionPath).doc(docId).update(data);
  }

  Future<void> deleteDocument(String collectionPath, String docId) {
    return _firestore.collection(collectionPath).doc(docId).delete();
  }

  Future<DocumentSnapshot> getTeacherById(String teacherId) {
    return _firestore.collection('users').doc(teacherId).get();
  }

  // --- دوال تخزين الملفات (Firebase Storage) ---

  /// دالة لاختيار صورة من معرض الصور
  Future<XFile?> pickImage() async {
    return await _picker.pickImage(source: ImageSource.gallery);
  }

  /// دالة لاختيار عدة صور من معرض الصور
  Future<List<XFile>> pickMultipleImages() async {
    return await _picker.pickMultiImage();
  }

  /// دالة لرفع صورة إلى Firebase Storage وإرجاع رابطها
  Future<String?> uploadImage(XFile image, String storagePath) async {
    try {
      final ref = _storage
          .ref(storagePath)
          .child('${DateTime.now().toIso8601String()}-${image.name}');
      await ref.putFile(File(image.path));
      return await ref.getDownloadURL();
    } catch (e) {
      if (kDebugMode) {
        print('خطأ أثناء رفع الصورة: $e');
      }
      return null;
    }
  }

  /// دالة لرفع عدة صور إلى Firebase Storage وإرجاع قائمة بروابطها
  Future<List<String>> uploadMultipleImages(
    List<XFile> images,
    String storagePath,
  ) async {
    final List<String> downloadUrls = [];
    try {
      // استخدام Future.wait لتنفيذ عمليات الرفع بالتوازي لتحسين الأداء
      await Future.wait(
        images.map((image) async {
          final ref = _storage
              .ref(storagePath)
              .child('${DateTime.now().toIso8601String()}-${image.name}');

          // رفع الملف
          await ref.putFile(File(image.path));

          // الحصول على رابط التحميل وإضافته إلى القائمة
          final url = await ref.getDownloadURL();
          downloadUrls.add(url);
        }),
      );
      return downloadUrls;
    } catch (e) {
      if (kDebugMode) {
        print('خطأ أثناء رفع الصور المتعددة: $e');
      }
      // في حالة الفشل، يمكن إرجاع قائمة فارغة أو التعامل مع الخطأ بطريقة أخرى
      return [];
    }
  }

  /// دالة لحذف صورة من Firebase Storage باستخدام رابطها
  Future<void> deleteImage(String imageUrl) async {
    if (imageUrl.isEmpty) return;
    try {
      final ref = _storage.refFromURL(imageUrl);
      await ref.delete();
    } catch (e) {
      // قد تفشل العملية إذا لم يكن الرابط صحيحًا أو إذا تم حذف الملف بالفعل
      if (kDebugMode) {
        print('خطأ أثناء حذف الصورة: $e');
      }
    }
  }

  // --- دوال خاصة بالرسوم والمدفوعات ---

  Stream<QuerySnapshot> getFeeTypes() {
    return _firestore.collection('fee_types').snapshots();
  }

  Future<void> assignFeeToStudent(
    String studentId,
    Map<String, dynamic> feeData,
  ) {
    return _firestore
        .collection('students')
        .doc(studentId)
        .collection('assigned_fees')
        .add(feeData);
  }

  Stream<QuerySnapshot> getStudentAssignedFees(String studentId) {
    return _firestore
        .collection('students')
        .doc(studentId)
        .collection('assigned_fees')
        .orderBy('created_at', descending: true)
        .snapshots();
  }

  Future<void> addPaymentAndUpdateFee({
    required String studentId,
    required String assignedFeeId,
    required Map<String, dynamic> paymentData,
  }) async {
    final paymentAmount = paymentData['amount'] as num;

    await _firestore
        .collection('students')
        .doc(studentId)
        .collection('payments')
        .add(paymentData);

    final feeDocRef = _firestore
        .collection('students')
        .doc(studentId)
        .collection('assigned_fees')
        .doc(assignedFeeId);

    return _firestore.runTransaction((transaction) async {
      final snapshot = await transaction.get(feeDocRef);
      if (!snapshot.exists) {
        throw Exception("الرسوم المحددة غير موجودة!");
      }
      final currentPaid = (snapshot.data()!['amount_paid'] ?? 0) as num;
      final newPaidAmount = currentPaid + paymentAmount;
      transaction.update(feeDocRef, {'amount_paid': newPaidAmount});
    });
  }

  Stream<QuerySnapshot> getStudentPayments(String studentId) {
    return _firestore
        .collection('students')
        .doc(studentId)
        .collection('payments')
        .orderBy('payment_date', descending: true)
        .snapshots();
  }

  // --- دوال مجمعة للتقارير ---

  /// دالة للحصول على التفاصيل المالية للطالب كـ Stream لتحديث البيانات تلقائياً
  /// تجمع بين بيانات الرسوم المعينة والمدفوعات وتحسب الإجماليات
  Stream<Map<String, dynamic>> getStudentFinancialDetails(String studentId) {
    // استخدام StreamController لدمج البيانات من مصدرين (stream) مختلفين
    final controller = StreamController<Map<String, dynamic>>();

    // Stream الخاص بالرسوم المعينة للطالب
    final feesStream =
        _firestore
            .collection('students')
            .doc(studentId)
            .collection('assigned_fees')
            .snapshots();

    // Stream الخاص بالمدفوعات
    final paymentsStream =
        _firestore
            .collection('students')
            .doc(studentId)
            .collection('payments')
            .snapshots();

    // متغيرات لتخزين آخر بيانات تم استقبالها من كل stream
    QuerySnapshot? lastFeesSnapshot;
    QuerySnapshot? lastPaymentsSnapshot;

    // دالة التحديث التي يتم استدعاؤها عند وصول بيانات جديدة من أي stream
    void update() {
      // التأكد من أن كلا المجموعتين من البيانات قد تم استلامهما مرة واحدة على الأقل
      if (lastFeesSnapshot == null || lastPaymentsSnapshot == null) return;

      final assignedFees =
          lastFeesSnapshot!.docs
              .map((doc) => doc.data() as Map<String, dynamic>)
              .toList();
      final payments =
          lastPaymentsSnapshot!.docs
              .map((doc) => doc.data() as Map<String, dynamic>)
              .toList();

      double totalAssigned = 0;
      double totalPaid = 0;

      for (var fee in assignedFees) {
        totalAssigned += (fee['amount_total'] as num?) ?? 0.0;
        // يتم حساب الإجمالي المدفوع من collection الرسوم نفسها لضمان الدقة
        totalPaid += (fee['amount_paid'] as num?) ?? 0.0;
      }

      // إضافة البيانات المجمعة والمحسوبة إلى الـ stream
      controller.add({
        'assignedFees': assignedFees,
        'payments': payments,
        'totalAssigned': totalAssigned,
        'totalPaid': totalPaid,
        'totalRemaining': totalAssigned - totalPaid,
      });
    }

    // الاستماع للتغيرات في stream الرسوم
    final feesSubscription = feesStream.listen((snapshot) {
      lastFeesSnapshot = snapshot;
      update(); // استدعاء دالة التحديث
    });

    // الاستماع للتغيرات في stream المدفوعات
    final paymentsSubscription = paymentsStream.listen((snapshot) {
      lastPaymentsSnapshot = snapshot;
      update(); // استدعاء دالة التحديث
    });

    // عند إلغاء الاشتراك في الـ stream المدمج، يتم إلغاء الاشتراكات الداخلية
    controller.onCancel = () {
      feesSubscription.cancel();
      paymentsSubscription.cancel();
    };

    return controller.stream;
  }

  /// دالة للحصول على درجات الطالب كـ Stream لتحديث البيانات تلقائياً
  Stream<List<Map<String, dynamic>>> getStudentGrades(String studentId) {
    final gradesStream =
        _firestore
            .collection('students')
            .doc(studentId)
            .collection('grades')
            .snapshots();
    // تحويل كل snapshot إلى قائمة من الخرائط
    return gradesStream.map(
      (snapshot) => snapshot.docs.map((doc) => doc.data()).toList(),
    );
  }

  /// دالة للحصول على سجل حضور الطالب كـ Stream لتحديث البيانات تلقائياً
  Stream<List<Map<String, dynamic>>> getStudentAttendance(String studentId) {
    final attendanceStream =
        _firestore
            .collection('students')
            .doc(studentId)
            .collection('attendance')
            .orderBy('date', descending: true)
            .snapshots();
    // تحويل كل snapshot إلى قائمة من الخرائط
    return attendanceStream.map(
      (snapshot) => snapshot.docs.map((doc) => doc.data()).toList(),
    );
  }

  /// دالة لجلب تقرير شامل عن الفصل باستخدام دالة سحابية (Cloud Function)
  /// هذا يحل مشكلة N+1 ويحسن الأداء بشكل كبير.
  Future<List<Map<String, dynamic>>> getClassComprehensiveReportData(
    String classId,
  ) async {
    try {
      // الحصول على الدالة السحابية المسماة 'getClassComprehensiveReport'
      final HttpsCallable callable = _functions.httpsCallable(
        'getClassComprehensiveReport',
      );

      // استدعاء الدالة وتمرير 'classId' كمعامل
      final HttpsCallableResult result = await callable.call({
        'classId': classId,
      });

      // نتيجة الدالة السحابية تكون في حقل 'data'
      // يتم تحويلها إلى قائمة من الخرائط الديناميكية بطريقة آمنة
      final List<dynamic> reportData = result.data;
      return List<Map<String, dynamic>>.from(reportData);
    } on FirebaseFunctionsException catch (e) {
      // معالجة الأخطاء الخاصة بالدوال السحابية
      if (kDebugMode) {
        print(
          'حدث خطأ أثناء استدعاء الدالة السحابية: ${e.code} - ${e.message}',
        );
      }
      // إرجاع قائمة فارغة في حالة الخطأ
      return [];
    } catch (e) {
      // معالجة أي أخطاء أخرى
      if (kDebugMode) {
        print('حدث خطأ غير متوقع: $e');
      }
      return [];
    }
  }

  //region Competition Management
  Future<void> addCompetition(Map<String, dynamic> competitionData) async {
    await _firestore.collection('competitions').add({
      ...competitionData,
      'createdAt': FieldValue.serverTimestamp(),
    });
  }

  Future<void> updateCompetition(
    String id,
    Map<String, dynamic> competitionData,
  ) async {
    await _firestore.collection('competitions').doc(id).update(competitionData);
  }

  Future<void> deleteCompetition(String id) async {
    await _firestore.collection('competitions').doc(id).delete();
  }
  //endregion

  // --- دوال خاصة بولي الأمر ---

  /// دالة لجلب بيانات جميع الأبناء المرتبطين بولي أمر معين
  /// تجمع ملخصات لكل ابن (الدرجات، الحضور، الحالة المالية)
  Future<List<Map<String, dynamic>>> getGuardianChildrenData(
    String guardianId,
  ) async {
    try {
      // 1. جلب قائمة الطلاب المرتبطين بولي الأمر
      final studentsSnapshot =
          await _firestore
              .collection('students')
              .where('guardianId', isEqualTo: guardianId)
              .get();

      if (studentsSnapshot.docs.isEmpty) {
        return []; // لا يوجد أبناء لعرضهم
      }

      // 2. مصفوفة من الوعود لجلب البيانات لكل ابن بالتوازي
      final childrenDataPromises =
          studentsSnapshot.docs.map((studentDoc) async {
            final studentId = studentDoc.id;
            final studentData = studentDoc.data();

            // 3. جلب البيانات لكل ابن (نستخدم .get() هنا لأننا نريد لقطة بيانات حالية للداشبورد)
            final financialDetailsFuture =
                getStudentFinancialDetails(
                  studentId,
                ).first; // نأخذ أول قيمة من الـ Stream
            final gradesFuture = getStudentGrades(studentId).first;
            final attendanceFuture = getStudentAttendance(studentId).first;

            final results = await Future.wait([
              financialDetailsFuture,
              gradesFuture,
              attendanceFuture,
            ]);

            final financialDetails = results[0] as Map<String, dynamic>;
            final grades = results[1] as List<Map<String, dynamic>>;
            final attendance = results[2] as List<Map<String, dynamic>>;

            // 4. بناء كائن ملخص لكل ابن
            return {
              'studentInfo': studentData,
              'financialSummary': {
                'totalRemaining': financialDetails['totalRemaining'],
              },
              'gradesSummary': {
                'count': grades.length,
                // يمكن إضافة منطق لحساب متوسط الدرجات هنا إذا لزم الأمر
              },
              'attendanceSummary': {
                'absentDays':
                    attendance.where((a) => a['status'] == 'غائب').length,
              },
            };
          }).toList();

      // انتظار اكتمال جميع العمليات
      return await Future.wait(childrenDataPromises);
    } catch (e) {
      if (kDebugMode) {
        print('خطأ أثناء جلب بيانات أبناء ولي الأمر: $e');
      }
      return [];
    }
  }

  // --- دوال خاصة بالجدول الدراسي ---

  /// دالة لجلب الجدول الدراسي للطالب بناءً على فصله الدراسي
  Future<Map<String, dynamic>> getStudentTimetable(String studentId) async {
    try {
      // 1. جلب بيانات الطالب للحصول على معرف الفصل
      final studentDoc =
          await _firestore.collection('students').doc(studentId).get();
      if (!studentDoc.exists || studentDoc.data() == null) {
        throw Exception('لم يتم العثور على بيانات الطالب.');
      }
      final classId = studentDoc.data()!['classId'];
      if (classId == null) {
        throw Exception('الطالب غير مسجل في فصل دراسي.');
      }

      // 2. جلب الجدول الدراسي مباشرة باستخدام معرف الفصل كمعرف للمستند
      final timetableDoc =
          await _firestore.collection('timetables').doc(classId).get();

      if (!timetableDoc.exists) {
        // لا يوجد جدول دراسي لهذا الفصل
        return {};
      }

      // 3. إرجاع بيانات الجدول
      return timetableDoc.data() ?? {};
    } catch (e) {
      if (kDebugMode) {
        print('خطأ أثناء جلب الجدول الدراسي: $e');
      }
      // إعادة رمي الخطأ ليتم التعامل معه في FutureBuilder
      rethrow;
    }
  }

  // --- دوال خاصة بالإعلانات ---

  /// دالة لجلب قائمة الإعلانات العامة من المدرسة كـ Stream
  Stream<QuerySnapshot> getAnnouncements() {
    return _firestore
        .collection('announcements')
        .orderBy('createdAt', descending: true)
        .snapshots();
  }

  // --- دوال خاصة بالأنشطة ---

  /// دالة لجلب قائمة الأنشطة المدرسية كـ Stream
  Stream<QuerySnapshot> getActivitiesStream() {
    return _firestore
        .collection('activities')
        .orderBy('date', descending: true)
        .snapshots();
  }

  // --- دوال خاصة بالواجبات المدرسية ---

  /// دالة لجلب الواجبات المدرسية للطالب بناءً على فصله الدراسي
  Stream<QuerySnapshot> getStudentAssignments(String studentId) {
    // استخدام StreamController للتعامل مع الخطوات غير المتزامنة
    final controller = StreamController<QuerySnapshot>();

    _firestore
        .collection('students')
        .doc(studentId)
        .get()
        .then((studentDoc) {
          if (!studentDoc.exists || studentDoc.data() == null) {
            controller.addError('لم يتم العثور على بيانات الطالب.');
            controller.close();
            return;
          }
          final classId = studentDoc.data()!['classId'];
          if (classId == null) {
            controller.addError('الطالب غير مسجل في فصل دراسي.');
            controller.close();
            return;
          }

          // بعد الحصول على classId، نبدأ بالاستماع إلى الواجبات الخاصة بهذا الفصل
          final assignmentsStream =
              _firestore
                  .collection('assignments')
                  .where('classId', isEqualTo: classId)
                  .orderBy(
                    'dueDate',
                    descending: false,
                  ) // ترتيب حسب تاريخ التسليم
                  .snapshots();

          // ربط stream الواجبات بالـ controller الخاص بنا
          final subscription = assignmentsStream.listen(
            (data) => controller.add(data),
            onError: (error) => controller.addError(error),
            onDone: () => controller.close(),
          );

          // عند إلغاء الاشتراك، نلغي الاشتراك الداخلي
          controller.onCancel = () {
            subscription.cancel();
          };
        })
        .catchError((error) {
          controller.addError(error);
          controller.close();
        });

    return controller.stream;
  }

  // --- دوال خاصة بلوحة التحكم (Dashboard) ---

  /// دالة مجمعة لجلب بيانات لوحة التحكم الرئيسية للطالب بكفاءة
  Future<Map<String, dynamic>> getStudentDashboardData(String studentId) async {
    try {
      // 1. جلب بيانات الطالب الأساسية
      final studentDoc =
          await _firestore.collection('students').doc(studentId).get();
      if (!studentDoc.exists) throw Exception('لم يتم العثور على الطالب.');
      final studentData = studentDoc.data()!;
      final classId = studentData['classId'];

      // 2. جلب الواجبات القادمة (التي لم يحن موعد تسليمها بعد)
      List<Map<String, dynamic>> upcomingAssignments = [];
      if (classId != null) {
        final assignmentsSnapshot =
            await _firestore
                .collection('assignments')
                .where('classId', isEqualTo: classId)
                .where('dueDate', isGreaterThanOrEqualTo: Timestamp.now())
                .orderBy('dueDate', descending: false)
                .limit(2) // جلب أقرب واجبين فقط
                .get();
        upcomingAssignments =
            assignmentsSnapshot.docs.map((doc) => doc.data()).toList();
      }

      // 3. جلب الملخص المالي (نأخذ أول قيمة من الـ Stream)
      final financialDetails =
          await getStudentFinancialDetails(studentId).first;

      // 4. جلب آخر إعلان
      final announcementsSnapshot =
          await _firestore
              .collection('announcements')
              .orderBy('createdAt', descending: true)
              .limit(1)
              .get();
      final latestAnnouncement =
          announcementsSnapshot.docs.isNotEmpty
              ? announcementsSnapshot.docs.first.data()
              : null;

      // 5. تجميع كل البيانات في خريطة واحدة
      return {
        'studentInfo': studentData,
        'upcomingAssignments': upcomingAssignments,
        'financialSummary': {
          'totalRemaining': financialDetails['totalRemaining'] ?? 0.0,
        },
        'latestAnnouncement': latestAnnouncement,
      };
    } catch (e) {
      if (kDebugMode) {
        print('خطأ أثناء جلب بيانات لوحة التحكم: $e');
      }
      rethrow;
    }
  }

  // --- دوال خاصة بإدارة الموظفين ---

  /// (تم التعديل) دالة لإضافة موظف جديد (معلم أو إداري) من جانب العميل
  Future<void> addStaff({
    required String name,
    required String email,
    required String password,
    required String jobTitle,
    required String bio,
    required String role, // 'teacher', 'staff'
    XFile? image,
  }) async {
    FirebaseApp? tempApp;
    try {
      tempApp = await Firebase.initializeApp(
        name: 'tempStaffCreation',
        options: Firebase.app().options,
      );
      final tempAuth = FirebaseAuth.instanceFor(app: tempApp);

      final userCredential = await tempAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      final user = userCredential.user;
      if (user == null) {
        throw Exception('فشل إنشاء حساب الموظف في نظام المصادقة.');
      }
      await user.updateDisplayName(name);
      final staffId = user.uid;

      String? imageUrl;
      if (image != null) {
        imageUrl = await uploadImage(image, 'staff_profiles/$staffId');
      }

      await _firestore.collection('users').doc(staffId).set({
        'name': name,
        'email': email,
        'role': role,
        'jobTitle': jobTitle,
        'bio': bio,
        'profileImageUrl': imageUrl,
        'createdAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error adding staff client-side: $e');
      }
      rethrow;
    } finally {
      if (tempApp != null) {
        await tempApp.delete();
      }
    }
  }

  /// دالة لتحديث بيانات موظف حالي
  Future<void> updateStaff({
    required UserModel staff,
    required String name,
    required String jobTitle,
    required String bio,
    required String role,
    XFile? newImage,
  }) async {
    try {
      String? imageUrl = staff.profileImageUrl;

      // 1. رفع الصورة الجديدة إذا تم اختيارها وحذف القديمة
      if (newImage != null) {
        // حذف الصورة القديمة أولاً إذا كانت موجودة
        if (staff.profileImageUrl != null &&
            staff.profileImageUrl!.isNotEmpty) {
          await deleteImage(staff.profileImageUrl!);
        }
        // رفع الصورة الجديدة
        imageUrl = await uploadImage(newImage, 'staff_profiles/${staff.id}');
      }

      // 2. تحديث البيانات في Firestore
      await _firestore.collection('users').doc(staff.id).update({
        'name': name,
        'jobTitle': jobTitle,
        'bio': bio,
        'role': role,
        'profileImageUrl': imageUrl,
      });
    } catch (e) {
      // إعادة رمي الخطأ ليتم التعامل معه في الواجهة
      rethrow;
    }
  }

  // ================== Assignments Management ==================

  /// Fetches a stream of all assignments.
  Stream<List<AssignmentModel>> getAssignmentsStream() {
    return _firestore
        .collection('assignments')
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => AssignmentModel.fromFirestore(doc))
                  .toList(),
        );
  }

  /// Adds a new assignment to Firestore.
  Future<void> addAssignment({
    required String title,
    required String description,
    required String subjectName,
    required DateTime dueDate,
    String? classId, // Optional: To assign to a specific class
  }) async {
    try {
      await _firestore.collection('assignments').add({
        'title': title,
        'description': description,
        'subjectName': subjectName,
        'dueDate': Timestamp.fromDate(dueDate),
        'createdAt': Timestamp.now(),
        'classId': classId,
      });
    } catch (e) {
      // Rethrow the error to be handled in the UI layer
      rethrow;
    }
  }

  /// Updates an existing assignment in Firestore.
  Future<void> updateAssignment({
    required String id,
    required String title,
    required String description,
    required String subjectName,
    required DateTime dueDate,
    String? classId,
  }) async {
    try {
      await _firestore.collection('assignments').doc(id).update({
        'title': title,
        'description': description,
        'subjectName': subjectName,
        'dueDate': Timestamp.fromDate(dueDate),
        'classId': classId,
      });
    } catch (e) {
      rethrow;
    }
  }

  /// إضافة واجب محسن مع النماذج الجديدة
  Future<void> addAssignmentEnhanced(AssignmentModel assignment) async {
    try {
      final docRef = _firestore.collection('assignments').doc();
      final enhancedAssignment = assignment.copyWith(id: docRef.id);
      await docRef.set(enhancedAssignment.toMap());
    } catch (e) {
      rethrow;
    }
  }

  /// تحديث واجب محسن مع النماذج الجديدة
  Future<void> updateAssignmentEnhanced(AssignmentModel assignment) async {
    try {
      await _firestore
          .collection('assignments')
          .doc(assignment.id)
          .update(assignment.toMap());
    } catch (e) {
      rethrow;
    }
  }

  /// Deletes an assignment from Firestore.
  Future<void> deleteAssignment(String id) async {
    try {
      await _firestore.collection('assignments').doc(id).delete();
    } catch (e) {
      rethrow;
    }
  }

  /// Fetches assignments for a specific student based on their class.
  Stream<List<AssignmentModel>> getStudentAssignmentsStream(
    String studentId,
  ) async* {
    try {
      final studentDoc =
          await _firestore.collection('students').doc(studentId).get();
      if (!studentDoc.exists || studentDoc.data()?['classId'] == null) {
        yield [];
        return;
      }
      final classId = studentDoc.data()!['classId'];
      yield* _firestore
          .collection('assignments')
          .where('classId', isEqualTo: classId)
          .snapshots()
          .map(
            (snapshot) =>
                snapshot.docs
                    .map((doc) => AssignmentModel.fromFirestore(doc))
                    .toList(),
          );
    } catch (e) {
      if (kDebugMode) {
        print('Error getting student assignments: $e');
      }
      yield [];
    }
  }

  /// Checks if a student has submitted a solution for a specific assignment.
  Future<bool> isAssignmentSubmitted(
    String studentId,
    String assignmentId,
  ) async {
    // This is a placeholder. In a real app, you would have a 'submissions' sub-collection.
    // For now, we'll return false.
    // Example of what it might look like:
    /*
    final submissionDoc = await _firestore
        .collection('assignments')
        .doc(assignmentId)
        .collection('submissions')
        .doc(studentId)
        .get();
    return submissionDoc.exists;
    */
    return false;
  }

  // ================== Fee Types Management ==================

  /// Fetches a stream of all fee types.
  Stream<List<FeeTypeModel>> getFeeTypesStream() {
    return _firestore
        .collection('fee_types')
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => FeeTypeModel.fromMap(doc.data(), doc.id))
                  .toList(),
        );
  }

  /// Adds a new fee type to Firestore.
  Future<void> addFeeType(FeeTypeModel feeType) async {
    try {
      await _firestore.collection('fee_types').add(feeType.toMap());
    } catch (e) {
      rethrow;
    }
  }

  /// Updates an existing fee type in Firestore.
  Future<void> updateFeeType(FeeTypeModel feeType) async {
    try {
      await _firestore
          .collection('fee_types')
          .doc(feeType.id)
          .update(feeType.toMap());
    } catch (e) {
      rethrow;
    }
  }

  /// Deletes a fee type from Firestore.
  Future<void> deleteFeeType(String id) async {
    try {
      await _firestore.collection('fee_types').doc(id).delete();
    } catch (e) {
      rethrow;
    }
  }

  /// Fetches the count of documents in a collection based on a role or list of roles.
  Future<int> getUsersCountByRole(dynamic role) async {
    Query query = _firestore.collection('users');

    if (role is String) {
      query = query.where('role', isEqualTo: role);
    } else if (role is List<String>) {
      query = query.where('role', whereIn: role);
    }

    try {
      final aggregateQuery = await query.count().get();
      return aggregateQuery.count ?? 0;
    } catch (e) {
      if (kDebugMode) {
        print("Error fetching count for role(s) $role: $e");
      }
      return 0;
    }
  }

  // --- دوال خاصة بإدارة المسؤولين ---

  /// يتحقق مما إذا كان هناك أي مسؤولين في قاعدة البيانات
  Future<bool> areThereAnyAdmins() async {
    try {
      final adminQuery =
          await _firestore
              .collection('users')
              .where('role', isEqualTo: 'admin')
              .limit(1)
              .get();
      return adminQuery.docs.isNotEmpty;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking for admins: $e');
      }
      // في حالة حدوث خطأ، نفترض وجود مسؤول كإجراء وقائي
      return true;
    }
  }

  /// يجلب قائمة المستخدمين بناءً على دورهم المحدد
  Stream<List<UserModel>> getUsersByRoleStream(List<String> roles) {
    return _firestore
        .collection('users')
        .where('role', whereIn: roles)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => UserModel.fromMap(doc.data(), doc.id))
                  .toList(),
        );
  }

  /// (تم التعديل) يضيف مسؤول جديد من جانب العميل (بدون دوال سحابية)
  Future<void> addAdmin(String name, String email, String password) async {
    // هذا حل بديل لتجنب الحاجة لخطة Blaze.
    // نستخدم نسخة ثانوية من Firebase لمنع تسجيل خروج المسؤول الحالي.
    FirebaseApp? tempApp;
    try {
      // 1. إنشاء نسخة مؤقتة من التطبيق لتسجيل المستخدم الجديد
      tempApp = await Firebase.initializeApp(
        name: 'tempAdminCreation',
        options: Firebase.app().options,
      );
      final tempAuth = FirebaseAuth.instanceFor(app: tempApp);

      // 2. إنشاء المستخدم في Firebase Authentication عبر النسخة المؤقتة
      final userCredential = await tempAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      final user = userCredential.user;
      if (user == null) {
        throw Exception('فشل إنشاء المستخدم في نظام المصادقة.');
      }

      // 3. تحديث اسم العرض للمستخدم الجديد
      await user.updateDisplayName(name);

      // 4. إنشاء مستند للمستخدم الجديد في Firestore لتحديد دوره
      await _firestore.collection('users').doc(user.uid).set({
        'name': name,
        'email': email,
        'role': 'admin',
        'createdAt': FieldValue.serverTimestamp(),
      });

      // ملاحظة: لا يمكننا تعيين Custom Claims من العميل.
      // يجب أن تعتمد قواعد الأمان على التحقق من الدور في مستند Firestore.
    } catch (e) {
      // إعادة رمي الخطأ ليتم عرضه في الواجهة
      if (kDebugMode) {
        print('Error adding admin client-side: $e');
      }
      rethrow;
    } finally {
      // 5. حذف النسخة المؤقتة من التطبيق لتنظيف الموارد
      if (tempApp != null) {
        await tempApp.delete();
      }
    }
  }

  /// يحدث بيانات المسؤول في Firestore
  Future<void> updateAdmin(String adminId, String newName) async {
    try {
      await _firestore.collection('users').doc(adminId).update({
        'name': newName,
      });
    } catch (e) {
      rethrow;
    }
  }

  /// (تم التعديل) يحذف المسؤول من جانب العميل
  Future<void> deleteAdmin(String adminId) async {
    // منع المسؤول من حذف نفسه
    if (_auth.currentUser!.uid == adminId) {
      throw Exception('لا يمكنك حذف حسابك الخاص.');
    }
    try {
      // حذف مستند المستخدم من مجموعة 'users'
      await _firestore.collection('users').doc(adminId).delete();
    } catch (e) {
      rethrow;
    }
  }

  // --- دوال خاصة بإدارة أولياء الأمور ---

  /// يجلب قائمة أولياء الأمور
  Stream<List<GuardianModel>> getGuardiansStream() {
    return _firestore
        .collection('guardians')
        .orderBy('created_at', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => GuardianModel.fromMap(doc.data(), doc.id))
                  .toList(),
        );
  }

  /// يجلب قائمة جميع الطلاب
  Stream<List<StudentModel>> getAllStudentsStream() {
    return _firestore
        .collection('students')
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => StudentModel.fromMap(doc.data(), doc.id))
                  .toList(),
        );
  }

  /// Stream to get all students of a specific class.
  Stream<List<StudentModel>> getStudentsByClassStream(String classId) {
    return _firestore
        .collection('students')
        .where('classId', isEqualTo: classId)
        .orderBy('name')
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map((doc) => StudentModel.fromMap(doc.data(), doc.id))
              .toList();
        });
  }

  /// (تم التعديل) يضيف ولي أمر جديد من جانب العميل
  Future<void> addGuardian(
    String name,
    String email,
    String phone,
    String password,
  ) async {
    FirebaseApp? tempApp;
    try {
      tempApp = await Firebase.initializeApp(
        name: 'tempGuardianCreation',
        options: Firebase.app().options,
      );
      final tempAuth = FirebaseAuth.instanceFor(app: tempApp);

      final userCredential = await tempAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      final user = userCredential.user;
      if (user == null) {
        throw Exception('فشل إنشاء حساب ولي الأمر في نظام المصادقة.');
      }
      await user.updateDisplayName(name);

      // إنشاء مستند ولي الأمر في Firestore
      await _firestore.collection('guardians').doc(user.uid).set({
        'name': name,
        'email': email,
        'phone_number': phone,
        'linked_students': [],
        'role': 'guardian',
        'created_at': FieldValue.serverTimestamp(),
      });

      // إنشاء مستند مطابق في 'users'
      await _firestore.collection('users').doc(user.uid).set({
        'displayName': name,
        'email': email,
        'role': 'guardian',
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error adding guardian client-side: $e');
      }
      rethrow;
    } finally {
      if (tempApp != null) {
        await tempApp.delete();
      }
    }
  }

  /// يحدث بيانات ولي أمر
  Future<void> updateGuardian(String uid, String name, String phone) async {
    try {
      await _firestore.collection('guardians').doc(uid).update({
        'name': name,
        'phone_number': phone,
      });
    } catch (e) {
      rethrow;
    }
  }

  /// (تم التعديل) يحذف ولي أمر من جانب العميل
  Future<void> deleteGuardian(String uid) async {
    try {
      // حذف مستند ولي الأمر من مجموعة 'guardians'
      await _firestore.collection('guardians').doc(uid).delete();
      // حذف مستند المستخدم من مجموعة 'users'
      await _firestore.collection('users').doc(uid).delete();
    } catch (e) {
      rethrow;
    }
  }

  /// يربط الطلاب بولي الأمر
  Future<void> linkStudentsToGuardian(
    String guardianId,
    List<String> studentIds,
  ) async {
    try {
      await _firestore.collection('guardians').doc(guardianId).update({
        'linked_students': studentIds,
      });
    } catch (e) {
      rethrow;
    }
  }

  // --- دوال جديدة لجلب البيانات الحقيقية للطلاب ---

  /// جلب درجات الطالب الحقيقية مع تفاصيل الامتحانات
  ///
  /// هذه الدالة تجلب جميع درجات الطالب من قاعدة البيانات مع:
  /// - تفاصيل الامتحان (الاسم، النوع، التاريخ)
  /// - معلومات المادة والمعلم
  /// - متوسط الصف والترتيب
  /// - ملاحظات المعلم
  Future<List<Map<String, dynamic>>> getStudentDetailedGrades(
    String studentId,
  ) async {
    try {
      final List<Map<String, dynamic>> detailedGrades = [];

      // 1. جلب درجات الطالب من المجموعة الفرعية
      final gradesSnapshot =
          await _firestore
              .collection('students')
              .doc(studentId)
              .collection('grades')
              .orderBy('examDate', descending: true)
              .get();

      // 2. لكل درجة، جلب تفاصيل الامتحان والمادة
      for (final gradeDoc in gradesSnapshot.docs) {
        final gradeData = gradeDoc.data();

        // جلب تفاصيل الامتحان
        Map<String, dynamic>? examData;
        if (gradeData['examId'] != null) {
          final examDoc =
              await _firestore
                  .collection('exams')
                  .doc(gradeData['examId'])
                  .get();
          examData = examDoc.exists ? examDoc.data() : null;
        }

        // جلب تفاصيل المادة
        Map<String, dynamic>? subjectData;
        if (gradeData['subjectId'] != null) {
          final subjectDoc =
              await _firestore
                  .collection('subjects')
                  .doc(gradeData['subjectId'])
                  .get();
          subjectData = subjectDoc.exists ? subjectDoc.data() : null;
        }

        // جلب معلومات المعلم
        Map<String, dynamic>? teacherData;
        if (gradeData['teacherId'] != null) {
          final teacherDoc =
              await _firestore
                  .collection('users')
                  .doc(gradeData['teacherId'])
                  .get();
          teacherData = teacherDoc.exists ? teacherDoc.data() : null;
        }

        // تجميع البيانات
        detailedGrades.add({
          'id': gradeDoc.id,
          'studentId': studentId,
          'grade': gradeData['grade'] ?? 0.0,
          'maxGrade': gradeData['maxGrade'] ?? 100.0,
          'examDate': gradeData['examDate'] ?? Timestamp.now(),
          'createdAt': gradeData['createdAt'] ?? Timestamp.now(),
          'updatedAt': gradeData['updatedAt'] ?? Timestamp.now(),

          // تفاصيل الامتحان
          'examId': gradeData['examId'],
          'examName': examData?['name'] ?? 'امتحان غير محدد',
          'examType': examData?['type'] ?? 'عادي',
          'examDuration': examData?['duration'] ?? 60,

          // تفاصيل المادة
          'subjectId': gradeData['subjectId'],
          'subjectName': subjectData?['name'] ?? 'مادة غير محددة',

          // تفاصيل المعلم
          'teacherId': gradeData['teacherId'],
          'teacherName': teacherData?['name'] ?? 'معلم غير محدد',

          // ملاحظات وتقييمات
          'teacherNotes': gradeData['teacherNotes'] ?? '',
          'performanceRating': gradeData['performanceRating'] ?? '',

          // إحصائيات الصف
          'classAverage': gradeData['classAverage'] ?? 0.0,
          'highestInClass': gradeData['highestInClass'] ?? 0.0,
          'lowestInClass': gradeData['lowestInClass'] ?? 0.0,
          'rankInClass': gradeData['rankInClass'] ?? 0,
          'totalStudents': gradeData['totalStudents'] ?? 0,

          // معلومات أكاديمية
          'semester': gradeData['semester'] ?? 'الفصل الأول',
          'academicYear': gradeData['academicYear'] ?? '2024-2025',
          'isPublished': gradeData['isPublished'] ?? false,
          'publishedAt': gradeData['publishedAt'],
        });
      }

      return detailedGrades;
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في جلب درجات الطالب التفصيلية: $e');
      }
      rethrow;
    }
  }

  /// جلب إحصائيات أداء الطالب الشاملة
  ///
  /// تحسب وتجلب:
  /// - المعدل العام للطالب
  /// - عدد الامتحانات المجتازة والراسبة
  /// - ترتيب الطالب في الصف
  /// - نسبة التحسن عبر الوقت
  /// - أفضل وأسوأ المواد
  Future<Map<String, dynamic>> getStudentPerformanceStats(
    String studentId,
  ) async {
    try {
      // جلب جميع درجات الطالب
      final grades = await getStudentDetailedGrades(studentId);

      if (grades.isEmpty) {
        return {
          'totalExams': 0,
          'passedExams': 0,
          'failedExams': 0,
          'overallAverage': 0.0,
          'classRank': 0,
          'totalStudentsInClass': 0,
          'improvementPercentage': 0.0,
          'bestSubject': null,
          'worstSubject': null,
          'gradesBySubject': <String, List<double>>{},
          'gradesByExamType': <String, List<double>>{},
        };
      }

      // حساب الإحصائيات الأساسية
      final totalExams = grades.length;
      final passedExams =
          grades.where((g) => (g['grade'] as double) >= 60.0).length;
      final failedExams = totalExams - passedExams;

      // حساب المعدل العام
      final totalGrades = grades.fold<double>(
        0.0,
        (total, g) => total + (g['grade'] as double),
      );
      final overallAverage = totalGrades / totalExams;

      // تجميع الدرجات حسب المادة
      final Map<String, List<double>> gradesBySubject = {};
      final Map<String, List<double>> gradesByExamType = {};

      for (final grade in grades) {
        final subjectName = grade['subjectName'] as String;
        final examType = grade['examType'] as String;
        final gradeValue = grade['grade'] as double;

        gradesBySubject.putIfAbsent(subjectName, () => []).add(gradeValue);
        gradesByExamType.putIfAbsent(examType, () => []).add(gradeValue);
      }

      // العثور على أفضل وأسوأ المواد
      String? bestSubject, worstSubject;
      double bestAverage = 0.0, worstAverage = 100.0;

      gradesBySubject.forEach((subject, subjectGrades) {
        final average =
            subjectGrades.reduce((a, b) => a + b) / subjectGrades.length;
        if (average > bestAverage) {
          bestAverage = average;
          bestSubject = subject;
        }
        if (average < worstAverage) {
          worstAverage = average;
          worstSubject = subject;
        }
      });

      // حساب نسبة التحسن (مقارنة أول وآخر 3 امتحانات)
      double improvementPercentage = 0.0;
      if (grades.length >= 6) {
        final recentGrades =
            grades.take(3).map((g) => g['grade'] as double).toList();
        final oldGrades =
            grades
                .skip(grades.length - 3)
                .map((g) => g['grade'] as double)
                .toList();

        final recentAverage =
            recentGrades.reduce((a, b) => a + b) / recentGrades.length;
        final oldAverage = oldGrades.reduce((a, b) => a + b) / oldGrades.length;

        improvementPercentage =
            ((recentAverage - oldAverage) / oldAverage) * 100;
      }

      // الحصول على ترتيب الطالب (من آخر امتحان)
      final latestGrade = grades.first;
      final classRank = latestGrade['rankInClass'] as int? ?? 0;
      final totalStudentsInClass = latestGrade['totalStudents'] as int? ?? 0;

      return {
        'totalExams': totalExams,
        'passedExams': passedExams,
        'failedExams': failedExams,
        'overallAverage': overallAverage,
        'classRank': classRank,
        'totalStudentsInClass': totalStudentsInClass,
        'improvementPercentage': improvementPercentage,
        'bestSubject': bestSubject,
        'worstSubject': worstSubject,
        'bestAverage': bestAverage,
        'worstAverage': worstAverage,
        'gradesBySubject': gradesBySubject,
        'gradesByExamType': gradesByExamType,
      };
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في حساب إحصائيات أداء الطالب: $e');
      }
      rethrow;
    }
  }

  // --- دوال جديدة لجلب البيانات الحقيقية للمعلمين ---

  /// جلب إحصائيات المعلم الحقيقية من قاعدة البيانات
  ///
  /// تجلب:
  /// - عدد الامتحانات المُدارة
  /// - عدد الدرجات المعلقة للتصحيح
  /// - عدد الامتحانات القادمة
  /// - عدد المناهج المكتملة
  /// - عدد الطلاب المُدرَّسين
  Future<Map<String, dynamic>> getTeacherRealStats(String teacherId) async {
    try {
      // 1. جلب الامتحانات التي يديرها المعلم
      final examsSnapshot =
          await _firestore
              .collection('exams')
              .where('teacherId', isEqualTo: teacherId)
              .get();

      final totalExams = examsSnapshot.docs.length;

      // 2. حساب الامتحانات القادمة
      final now = Timestamp.now();
      final upcomingExams =
          examsSnapshot.docs.where((doc) {
            final examDate = doc.data()['examDate'] as Timestamp?;
            return examDate != null && examDate.compareTo(now) > 0;
          }).length;

      // 3. جلب الدرجات المعلقة للتصحيح
      int pendingGrades = 0;
      for (final examDoc in examsSnapshot.docs) {
        final examId = examDoc.id;

        // البحث في جميع مجموعات الطلاب الفرعية للدرجات
        final studentsSnapshot = await _firestore.collection('students').get();

        for (final studentDoc in studentsSnapshot.docs) {
          final gradesSnapshot =
              await _firestore
                  .collection('students')
                  .doc(studentDoc.id)
                  .collection('grades')
                  .where('examId', isEqualTo: examId)
                  .where('isPublished', isEqualTo: false)
                  .get();

          pendingGrades += gradesSnapshot.docs.length;
        }
      }

      // 4. جلب المناهج المكتملة
      final syllabusSnapshot =
          await _firestore
              .collection('exam_syllabuses')
              .where('teacherId', isEqualTo: teacherId)
              .where('isCompleted', isEqualTo: true)
              .get();

      final completedSyllabuses = syllabusSnapshot.docs.length;

      // 5. حساب عدد الطلاب المُدرَّسين (من خلال الفصول)
      final classesSnapshot =
          await _firestore
              .collection('classes')
              .where('teacherId', isEqualTo: teacherId)
              .get();

      int totalStudents = 0;
      for (final classDoc in classesSnapshot.docs) {
        final classId = classDoc.id;
        final studentsInClassSnapshot =
            await _firestore
                .collection('students')
                .where('classId', isEqualTo: classId)
                .get();

        totalStudents += studentsInClassSnapshot.docs.length;
      }

      // 6. جلب اسم المعلم
      final teacherDoc =
          await _firestore.collection('users').doc(teacherId).get();

      final teacherName =
          teacherDoc.exists
              ? (teacherDoc.data()?['name'] as String?) ?? 'معلم غير محدد'
              : 'معلم غير محدد';

      return {
        'teacherName': teacherName,
        'totalExams': totalExams,
        'pendingGrades': pendingGrades,
        'upcomingExams': upcomingExams,
        'completedSyllabuses': completedSyllabuses,
        'totalStudents': totalStudents,
        'classesCount': classesSnapshot.docs.length,
      };
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في جلب إحصائيات المعلم: $e');
      }
      rethrow;
    }
  }

  /// جلب بيانات أولياء الأمور الحقيقية مع تفاصيل الأبناء
  ///
  /// تجلب لكل ولي أمر:
  /// - قائمة الأبناء المرتبطين
  /// - ملخص أداء كل ابن
  /// - الحالة المالية لكل ابن
  /// - آخر الدرجات والحضور
  Future<List<Map<String, dynamic>>> getGuardianChildrenRealData(
    String guardianId,
  ) async {
    try {
      // 1. جلب بيانات ولي الأمر
      final guardianDoc =
          await _firestore.collection('guardians').doc(guardianId).get();

      if (!guardianDoc.exists) {
        throw Exception('ولي الأمر غير موجود');
      }

      final guardianData = guardianDoc.data()!;
      final linkedStudents = List<String>.from(
        guardianData['linked_students'] ?? [],
      );

      if (linkedStudents.isEmpty) {
        return [];
      }

      final List<Map<String, dynamic>> childrenData = [];

      // 2. لكل طالب مرتبط، جلب بياناته التفصيلية
      for (final studentId in linkedStudents) {
        // جلب بيانات الطالب الأساسية
        final studentDoc =
            await _firestore.collection('students').doc(studentId).get();

        if (!studentDoc.exists) continue;

        final studentInfo = studentDoc.data()!;
        studentInfo['id'] = studentId;

        // جلب آخر الدرجات
        final recentGradesSnapshot =
            await _firestore
                .collection('students')
                .doc(studentId)
                .collection('grades')
                .orderBy('examDate', descending: true)
                .limit(5)
                .get();

        final recentGrades =
            recentGradesSnapshot.docs.map((doc) => doc.data()).toList();

        // حساب المعدل الحالي
        double currentAverage = 0.0;
        if (recentGrades.isNotEmpty) {
          final totalGrades = recentGrades.fold<double>(
            0.0,
            (total, grade) => total + (grade['grade'] as double? ?? 0.0),
          );
          currentAverage = totalGrades / recentGrades.length;
        }

        // جلب بيانات الحضور الحديثة
        final attendanceSnapshot =
            await _firestore
                .collection('students')
                .doc(studentId)
                .collection('attendance')
                .orderBy('date', descending: true)
                .limit(30) // آخر 30 يوم
                .get();

        final attendanceRecords = attendanceSnapshot.docs;
        final totalDays = attendanceRecords.length;
        final presentDays =
            attendanceRecords
                .where((doc) => doc.data()['status'] == 'present')
                .length;
        final attendanceRate =
            totalDays > 0 ? (presentDays / totalDays) * 100 : 0.0;

        // جلب الحالة المالية
        final financialData = await getStudentFinancialDetails(studentId).first;

        // تجميع البيانات
        childrenData.add({
          'studentInfo': studentInfo,
          'financialSummary': {
            'totalRemaining': financialData['totalRemaining'] ?? 0.0,
            'totalAssigned': financialData['totalAssigned'] ?? 0.0,
            'totalPaid': financialData['totalPaid'] ?? 0.0,
          },
          'attendanceSummary': {
            'totalDays': totalDays,
            'presentDays': presentDays,
            'absentDays': totalDays - presentDays,
            'attendanceRate': attendanceRate,
          },
          'academicSummary': {
            'currentAverage': currentAverage,
            'recentGradesCount': recentGrades.length,
            'lastExamGrade':
                recentGrades.isNotEmpty ? recentGrades.first['grade'] : null,
            'lastExamSubject':
                recentGrades.isNotEmpty
                    ? recentGrades.first['subjectName']
                    : null,
          },
        });
      }

      return childrenData;
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في جلب بيانات أبناء ولي الأمر: $e');
      }
      rethrow;
    }
  }

  // --- دوال خاصة بإدارة المواد الدراسية ---

  Stream<List<Subject>> getSubjectsStream() {
    return _firestore
        .collection('subjects')
        .orderBy('name')
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs.map((doc) => Subject.fromFirestore(doc)).toList(),
        );
  }

  Stream<List<ClassModel>> getClassesStream() {
    return _firestore
        .collection('classes')
        .orderBy('name')
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => ClassModel.fromFirestore(doc))
                  .toList(),
        );
  }

  Future<void> updateClass(String classId, Map<String, dynamic> data) async {
    await _firestore.collection('classes').doc(classId).update(data);
  }

  Future<void> deleteClass(String classId) async {
    // TODO: يجب إضافة منطق للتعامل مع البيانات المرتبطة (الطلاب، المواد، إلخ) قبل الحذف.
    await _firestore.collection('classes').doc(classId).delete();
  }

  Future<void> addClass(
    String name, {
    String? teacherId,
    List<String>? studentIds,
  }) async {
    final classDoc = await _firestore.collection('classes').add({
      'name': name,
      'teacherId': teacherId,
    });

    if (studentIds != null && studentIds.isNotEmpty) {
      final batch = _firestore.batch();
      for (final studentId in studentIds) {
        final studentRef = _firestore.collection('students').doc(studentId);
        batch.update(studentRef, {'classId': classDoc.id});
      }
      await batch.commit();
    }
  }

  Future<void> assignStudentsToClass(
    String classId,
    List<String> studentIds,
  ) async {
    final batch = _firestore.batch();
    for (final studentId in studentIds) {
      final studentRef = _firestore.collection('students').doc(studentId);
      batch.update(studentRef, {'classId': classId});
    }
    await batch.commit();
  }

  Future<void> addSubject(String name, String classId) async {
    try {
      await _firestore.collection('subjects').add({
        'name': name,
        'classId': classId,
      });
    } catch (e) {
      rethrow;
    }
  }

  Future<void> updateSubject(String docId, String name, String classId) async {
    try {
      await _firestore.collection('subjects').doc(docId).update({
        'name': name,
        'classId': classId,
      });
    } catch (e) {
      rethrow;
    }
  }

  Future<void> deleteSubject(String docId) async {
    try {
      await _firestore.collection('subjects').doc(docId).delete();
    } catch (e) {
      rethrow;
    }
  }

  // --- دوال خاصة بإدارة الجداول الدراسية ---

  Stream<DocumentSnapshot> getTimetableStream(String classId) {
    return _firestore.collection('timetables').doc(classId).snapshots();
  }

  Future<void> updateTimetableSession(
    String classId,
    String day,
    int period,
    Map<String, dynamic> sessionData,
  ) {
    return _firestore.collection('timetables').doc(classId).set({
      '$day-$period': sessionData,
    }, SetOptions(merge: true));
  }

  Future<void> clearTimetableSession(String classId, String day, int period) {
    return _firestore.collection('timetables').doc(classId).set({
      '$day-$period': FieldValue.delete(),
    }, SetOptions(merge: true));
  }

  Future<Map<String, dynamic>?> checkTeacherScheduleConflict(
    String currentClassId,
    String day,
    int period,
    String teacherId,
  ) async {
    final querySnapshot =
        await _firestore
            .collection('timetables')
            .where(FieldPath.documentId, isNotEqualTo: currentClassId)
            .get();

    for (var doc in querySnapshot.docs) {
      final timetableData = doc.data();
      final sessionKey = '$day-$period';
      if (timetableData.containsKey(sessionKey)) {
        final sessionData = timetableData[sessionKey];
        if (sessionData is Map && sessionData['teacherId'] == teacherId) {
          final classDoc =
              await _firestore.collection('classes').doc(doc.id).get();
          return {'className': classDoc.data()?['name'] ?? 'غير معروف'};
        }
      }
    }
    return null;
  }

  // --- دوال خاصة بالإشعارات ---
  Future<void> sendNotification({
    required String title,
    required String message,
    required String target, // 'all', 'class', 'student'
    String? targetId,
  }) async {
    // (تحسين) يجب أن يستدعي هذا دالة سحابية لإرسال الإشعارات بشكل آمن وفعال.
    if (kDebugMode) {
      print(
        'FirebaseService: Preparing to send notification via Cloud Function.',
      );
      print(
        'Title: $title, Message: $message, Target: $target, TargetID: $targetId',
      );
    }

    try {
      final callable = _functions.httpsCallable('sendPushNotification');
      await callable.call({
        'title': title,
        'message': message,
        'target': target,
        'targetId': targetId,
      });
    } on FirebaseFunctionsException catch (e) {
      if (kDebugMode) {
        print('Error calling sendPushNotification function: ${e.message}');
      }
      throw Exception(e.message);
    } catch (e) {
      if (kDebugMode) {
        print('Generic error in sendNotification: $e');
      }
      throw Exception(
        'An unexpected error occurred while sending notification.',
      );
    }
  }

  /// دالة عامة لجلب قائمة فريدة من القيم لحقل معين في مجموعة.
  /// تُستخدم لجلب قائمة المواد للتصفية.
  Future<List<String>> getUniqueFieldValues(
    String collection,
    String field,
  ) async {
    try {
      final snapshot = await _firestore.collection(collection).get();
      final values =
          snapshot.docs.map((doc) => doc[field] as String).toSet().toList();
      return values;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting unique field values: $e');
      }
      return [];
    }
  }

  // --- دوال خاصة بالملاحظات ---

  /// دالة لجلب الملاحظات الخاصة بالمستخدم الحالي
  Stream<List<NoteModel>> getNotesStream({required String studentId}) {
    // This logic assumes a conversation between the student and some admin/teacher.
    // A more robust system would have a conversation ID.
    // For now, we'll query for conversations that include the student.
    return _firestore
        .collection('notes')
        .where('participants', arrayContains: studentId)
        .orderBy('timestamp', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => NoteModel.fromMap(doc.data(), doc.id))
                  .toList(),
        );
  }

  /// دالة لإرسال ملاحظة جديدة
  Future<void> sendNote(String content) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null || content.trim().isEmpty) {
      return;
    }

    final newNote = NoteModel(
      id: '', // سيتم إنشاؤه بواسطة Firestore
      title: 'ملاحظة من طالب', // عنوان افتراضي
      senderId: currentUser.uid,
      receiverId: 'admin', // إرسال إلى الإدارة
      content: content.trim(),
      timestamp: DateTime.now(),
      isRead: false,
      participants: [currentUser.uid, 'admin'], // لتسهيل الاستعلام
    );

    await _firestore.collection('notes').add(newNote.toMap());
  }

  /// دالة لجلب بيانات طالب معين بواسطة معرفه
  Stream<StudentModel> getStudentById(String uid) {
    return _firestore
        .collection('students')
        .doc(uid)
        .snapshots()
        .map((doc) => StudentModel.fromMap(doc.data()!, doc.id));
  }

  // --- دوال خاصة بالتواصل ---

  /// دالة لجلب رسائل التواصل الخاصة بولي أمر معين
  Stream<List<CommunicationModel>> getCommunicationStream(String guardianId) {
    return _firestore
        .collection('communications')
        .where('guardianId', isEqualTo: guardianId)
        .orderBy('timestamp', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => CommunicationModel.fromFirestore(doc))
                  .toList(),
        );
  }

  /// دالة لإرسال رسالة تواصل جديدة
  Future<void> sendCommunicationMessage(
    String guardianId,
    String message,
  ) async {
    await _firestore.collection('communications').add({
      'guardianId': guardianId,
      'message': message,
      'timestamp': FieldValue.serverTimestamp(),
      'isRead': false,
      'reply': '',
    });
  }

  // --- دوال إدارة الامتحانات ---

  /// إنشاء امتحان جديد
  Future<String> createExam(ExamModel exam) async {
    try {
      final docRef = await _firestore
          .collection('exams')
          .add(exam.toFirestore());
      return docRef.id;
    } catch (e) {
      if (kDebugMode) {
        print('Error creating exam: $e');
      }
      rethrow;
    }
  }

  /// تحديث بيانات امتحان
  Future<void> updateExam(String examId, ExamModel exam) async {
    try {
      await _firestore
          .collection('exams')
          .doc(examId)
          .update(exam.toFirestore());
    } catch (e) {
      if (kDebugMode) {
        print('Error updating exam: $e');
      }
      rethrow;
    }
  }

  /// حذف امتحان
  Future<void> deleteExam(String examId) async {
    try {
      // حذف جميع البيانات المرتبطة بالامتحان
      final batch = _firestore.batch();

      // حذف جلسات الامتحان
      final sessions =
          await _firestore
              .collection('examSessions')
              .where('examId', isEqualTo: examId)
              .get();
      for (final doc in sessions.docs) {
        batch.delete(doc.reference);
      }

      // حذف مناهج الامتحان
      final syllabi =
          await _firestore
              .collection('examSyllabi')
              .where('examId', isEqualTo: examId)
              .get();
      for (final doc in syllabi.docs) {
        batch.delete(doc.reference);
      }

      // حذف إدخالات الدرجات
      final gradeEntries =
          await _firestore
              .collection('gradeEntries')
              .where('examId', isEqualTo: examId)
              .get();
      for (final doc in gradeEntries.docs) {
        batch.delete(doc.reference);
      }

      // حذف الامتحان نفسه
      batch.delete(_firestore.collection('exams').doc(examId));

      await batch.commit();
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting exam: $e');
      }
      rethrow;
    }
  }

  /// جلب جميع الامتحانات
  Stream<List<ExamModel>> getAllExamsStream() {
    return _firestore
        .collection('exams')
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs.map((doc) => ExamModel.fromFirestore(doc)).toList(),
        );
  }

  /// جلب امتحان بمعرفه
  Future<ExamModel?> getExamById(String examId) async {
    try {
      final doc = await _firestore.collection('exams').doc(examId).get();
      if (doc.exists) {
        return ExamModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting exam: $e');
      }
      return null;
    }
  }

  /// جلب الامتحانات حسب السنة الدراسية
  Stream<List<ExamModel>> getExamsByAcademicYear(String academicYear) {
    return _firestore
        .collection('exams')
        .where('academicYear', isEqualTo: academicYear)
        .orderBy('startDate')
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs.map((doc) => ExamModel.fromFirestore(doc)).toList(),
        );
  }

  /// جلب الامتحانات الجارية
  Stream<List<ExamModel>> getOngoingExamsStream() {
    final now = DateTime.now();
    return _firestore
        .collection('exams')
        .where('status', isEqualTo: ExamStatus.ongoing.toString())
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => ExamModel.fromFirestore(doc))
                  .where((exam) => exam.isOngoing)
                  .toList(),
        );
  }

  /// جلب الامتحانات القادمة
  Stream<List<ExamModel>> getUpcomingExamsStream() {
    final now = Timestamp.now();
    return _firestore
        .collection('exams')
        .where('startDate', isGreaterThan: now)
        .orderBy('startDate')
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs.map((doc) => ExamModel.fromFirestore(doc)).toList(),
        );
  }

  /// تحديث حالة الامتحان
  Future<void> updateExamStatus(String examId, ExamStatus status) async {
    try {
      await _firestore.collection('exams').doc(examId).update({
        'status': status.toString(),
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error updating exam status: $e');
      }
      rethrow;
    }
  }

  // --- دوال إدارة الدرجات ---

  /// إنشاء إدخال درجات جديد
  Future<String> createGradeEntry(GradeEntry gradeEntry) async {
    try {
      final docRef = await _firestore
          .collection('gradeEntries')
          .add(gradeEntry.toFirestore());
      return docRef.id;
    } catch (e) {
      if (kDebugMode) {
        print('Error creating grade entry: $e');
      }
      rethrow;
    }
  }

  /// تحديث إدخال درجات
  Future<void> updateGradeEntry(String entryId, GradeEntry gradeEntry) async {
    try {
      await _firestore
          .collection('gradeEntries')
          .doc(entryId)
          .update(gradeEntry.toFirestore());
    } catch (e) {
      if (kDebugMode) {
        print('Error updating grade entry: $e');
      }
      rethrow;
    }
  }

  /// حذف إدخال درجات
  Future<void> deleteGradeEntry(String entryId) async {
    try {
      await _firestore.collection('gradeEntries').doc(entryId).delete();
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting grade entry: $e');
      }
      rethrow;
    }
  }

  /// جلب جميع إدخالات الدرجات
  Stream<List<GradeEntry>> getAllGradeEntriesStream() {
    return _firestore
        .collection('gradeEntries')
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => GradeEntry.fromFirestore(doc))
                  .toList(),
        );
  }

  /// جلب إدخالات الدرجات حسب الامتحان
  Stream<List<GradeEntry>> getGradeEntriesByExam(String examId) {
    return _firestore
        .collection('gradeEntries')
        .where('examId', isEqualTo: examId)
        .orderBy('createdAt')
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => GradeEntry.fromFirestore(doc))
                  .toList(),
        );
  }

  /// جلب إدخالات الدرجات حسب المعلم
  Stream<List<GradeEntry>> getGradeEntriesByTeacher(String teacherId) {
    return _firestore
        .collection('gradeEntries')
        .where('teacherId', isEqualTo: teacherId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => GradeEntry.fromFirestore(doc))
                  .toList(),
        );
  }

  /// جلب إدخال درجات بمعرفه
  Future<GradeEntry?> getGradeEntryById(String entryId) async {
    try {
      final doc =
          await _firestore.collection('gradeEntries').doc(entryId).get();
      if (doc.exists) {
        return GradeEntry.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting grade entry: $e');
      }
      return null;
    }
  }

  /// إرسال إدخال الدرجات للمراجعة
  Future<void> submitGradeEntry(String entryId) async {
    try {
      await _firestore.collection('gradeEntries').doc(entryId).update({
        'status': GradeEntryStatus.submitted.toString(),
        'submittedAt': Timestamp.now(),
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error submitting grade entry: $e');
      }
      rethrow;
    }
  }

  /// اعتماد إدخال الدرجات
  Future<void> approveGradeEntry(String entryId) async {
    try {
      await _firestore.collection('gradeEntries').doc(entryId).update({
        'status': GradeEntryStatus.approved.toString(),
        'approvedAt': Timestamp.now(),
        'approvedBy': _auth.currentUser?.uid,
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error approving grade entry: $e');
      }
      rethrow;
    }
  }

  /// رفض إدخال الدرجات
  Future<void> rejectGradeEntry(String entryId, String reason) async {
    try {
      await _firestore.collection('gradeEntries').doc(entryId).update({
        'status': GradeEntryStatus.rejected.toString(),
        'rejectionReason': reason,
        'rejectedAt': Timestamp.now(),
        'rejectedBy': _auth.currentUser?.uid,
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error rejecting grade entry: $e');
      }
      rethrow;
    }
  }
}
